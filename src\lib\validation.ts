/**
 * Input validation utilities for Text Formatter Pro
 */

import { TEXT_LIMITS, ERROR_MESSAGES } from '@/lib/constants';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  warning?: string;
}

/**
 * Validate text input against length and line limits
 */
export const validateTextInput = (text: string): ValidationResult => {
  // Check if text is too long
  if (text.length > TEXT_LIMITS.maxLength) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.textTooLong
    };
  }

  // Check if there are too many lines
  const lineCount = text.split(/\r\n|\r|\n/).length;
  if (lineCount > TEXT_LIMITS.maxLines) {
    return {
      isValid: false,
      error: ERROR_MESSAGES.tooManyLines
    };
  }

  // Show warnings for large inputs
  let warning: string | undefined;
  if (text.length > TEXT_LIMITS.warningLength) {
    warning = `Large text detected (${text.length.toLocaleString()} characters). Performance may be affected.`;
  } else if (lineCount > TEXT_LIMITS.warningLines) {
    warning = `Many lines detected (${lineCount.toLocaleString()} lines). Performance may be affected.`;
  }

  return {
    isValid: true,
    warning
  };
};

/**
 * Sanitize text input to prevent potential issues
 */
export const sanitizeTextInput = (text: string): string => {
  // Remove null bytes and other potentially problematic characters
  return text.replace(/\0/g, '').replace(/\uFEFF/g, '');
};

/**
 * Validate special characters input for removal
 */
export const validateSpecialCharacters = (characters: string): ValidationResult => {
  if (!characters) {
    return { isValid: true };
  }

  // Check for potentially dangerous regex characters
  const dangerousChars = /[\\^$*+?.()|[\]{}]/;
  if (dangerousChars.test(characters)) {
    return {
      isValid: true,
      warning: 'Some characters may need to be escaped for proper removal.'
    };
  }

  return { isValid: true };
};

/**
 * Check if clipboard API is available
 */
export const validateClipboardSupport = (): ValidationResult => {
  if (!navigator.clipboard) {
    return {
      isValid: false,
      error: 'Clipboard API is not supported in this browser. Please copy manually.'
    };
  }

  return { isValid: true };
};

/**
 * Validate browser capabilities
 */
export const validateBrowserCapabilities = (): {
  clipboard: boolean;
  localStorage: boolean;
  modernFeatures: boolean;
} => {
  const clipboard = !!navigator.clipboard;
  
  let localStorage = false;
  try {
    const test = 'test';
    window.localStorage.setItem(test, test);
    window.localStorage.removeItem(test);
    localStorage = true;
  } catch (e) {
    localStorage = false;
  }

  const modernFeatures = !!(
    window.requestAnimationFrame &&
    window.Promise &&
    Array.prototype.includes &&
    String.prototype.includes
  );

  return {
    clipboard,
    localStorage,
    modernFeatures
  };
};

/**
 * Validate and format error messages for user display
 */
export const formatErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return ERROR_MESSAGES.processingError;
};

/**
 * Check if text contains potentially sensitive information
 */
export const checkForSensitiveData = (text: string): ValidationResult => {
  const patterns = {
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    phone: /(\+\d{1,3}[- ]?)?\d{10}/g,
    ssn: /\b\d{3}-?\d{2}-?\d{4}\b/g,
    creditCard: /\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b/g
  };

  const found = [];
  
  if (patterns.email.test(text)) found.push('email addresses');
  if (patterns.phone.test(text)) found.push('phone numbers');
  if (patterns.ssn.test(text)) found.push('social security numbers');
  if (patterns.creditCard.test(text)) found.push('credit card numbers');

  if (found.length > 0) {
    return {
      isValid: true,
      warning: `Potential sensitive data detected: ${found.join(', ')}. Please ensure you want to process this information.`
    };
  }

  return { isValid: true };
};

/**
 * Comprehensive text validation
 */
export const validateText = (text: string): ValidationResult => {
  // First sanitize the input
  const sanitized = sanitizeTextInput(text);
  
  // Validate basic constraints
  const basicValidation = validateTextInput(sanitized);
  if (!basicValidation.isValid) {
    return basicValidation;
  }

  // Check for sensitive data
  const sensitiveDataCheck = checkForSensitiveData(sanitized);
  
  // Combine warnings
  const warnings = [basicValidation.warning, sensitiveDataCheck.warning]
    .filter(Boolean)
    .join(' ');

  return {
    isValid: true,
    warning: warnings || undefined
  };
};

export default {
  validateTextInput,
  sanitizeTextInput,
  validateSpecialCharacters,
  validateClipboardSupport,
  validateBrowserCapabilities,
  formatErrorMessage,
  checkForSensitiveData,
  validateText
};
