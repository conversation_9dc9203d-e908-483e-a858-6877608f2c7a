/**
 * TypeScript type definitions for Text Formatter Pro
 */

// Text statistics interface
export interface TextStatistics {
  words: number;
  charactersWithSpaces: number;
  charactersWithoutSpaces: number;
  lines: number;
  paragraphs: number;
  sentences: number;
  readingTimeMinutes: number;
  averageWordLength: number;
}

// Case conversion options
export type CaseConversionType = 'upper' | 'lower' | 'title' | 'sentence';

// Text formatting operations interface
export interface TextFormattingOperations {
  caseConversion?: CaseConversionType;
  removeExtraWhitespace?: boolean;
  normalizeLineBreaks?: boolean;
  stripSpecialCharacters?: boolean | string;
  trimWhitespace?: boolean;
  removeNumbers?: boolean;
  removePunctuation?: boolean;
}

// Keyboard shortcut interface
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  action: string;
  description: string;
}

// Application state interface
export interface AppState {
  inputText: string;
  outputText: string;
  statistics: TextStatistics;
  isProcessing: boolean;
  lastOperation?: string;
}

// Theme interface
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    accent: string;
  };
}

// Component props interfaces
export interface TextAreaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  readOnly?: boolean;
  className?: string;
  'aria-label'?: string;
}

export interface ButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  'aria-label'?: string;
}

export interface StatisticsCardProps {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
  className?: string;
}

export interface ControlPanelProps {
  onCaseConversion: (type: CaseConversionType) => void;
  onTextCleaning: (operation: keyof TextFormattingOperations) => void;
  onReset: () => void;
  onCopy: () => void;
  disabled?: boolean;
}

// Error handling interfaces
export interface AppError {
  message: string;
  code?: string;
  timestamp: Date;
}

// Settings interface
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  autoSave: boolean;
  showStatistics: boolean;
  enableKeyboardShortcuts: boolean;
  defaultOperations: TextFormattingOperations;
}

// Export utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
