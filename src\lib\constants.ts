/**
 * Constants and configuration for Text Formatter Pro
 */

import { KeyboardShortcut, AppSettings } from '@/types';

// Application metadata
export const APP_CONFIG = {
  name: 'Text Formatter Pro',
  version: '1.0.0',
  description: 'A web-based text formatting and manipulation tool',
  author: '<PERSON><PERSON> (chirag127)',
  repository: 'https://github.com/chirag127/Text-Formatter-Pro'
} as const;

// Keyboard shortcuts configuration
export const KEYBOARD_SHORTCUTS: KeyboardShortcut[] = [
  {
    key: 'u',
    ctrlKey: true,
    action: 'uppercase',
    description: 'Convert to UPPERCASE'
  },
  {
    key: 'l',
    ctrlKey: true,
    action: 'lowercase',
    description: 'Convert to lowercase'
  },
  {
    key: 't',
    ctrlKey: true,
    action: 'titlecase',
    description: 'Convert to Title Case'
  },
  {
    key: 's',
    ctrlKey: true,
    action: 'sentencecase',
    description: 'Convert to Sentence case'
  },
  {
    key: 'w',
    ctrlKey: true,
    action: 'removeWhitespace',
    description: 'Remove extra whitespace'
  },
  {
    key: 'n',
    ctrlKey: true,
    action: 'normalizeLineBreaks',
    description: 'Normalize line breaks'
  },
  {
    key: 'r',
    ctrlKey: true,
    action: 'reset',
    description: 'Reset/Clear text'
  },
  {
    key: 'c',
    ctrlKey: true,
    shiftKey: true,
    action: 'copy',
    description: 'Copy formatted text'
  },
  {
    key: 'Escape',
    action: 'clearSelection',
    description: 'Clear selection'
  }
];

// Default application settings
export const DEFAULT_SETTINGS: AppSettings = {
  theme: 'auto',
  autoSave: false,
  showStatistics: true,
  enableKeyboardShortcuts: true,
  defaultOperations: {
    trimWhitespace: true,
    normalizeLineBreaks: false,
    removeExtraWhitespace: false
  }
};

// Text processing limits
export const TEXT_LIMITS = {
  maxLength: 1000000, // 1 million characters
  maxLines: 50000,
  warningLength: 100000, // Show warning at 100k characters
  warningLines: 10000
} as const;

// UI constants
export const UI_CONSTANTS = {
  debounceDelay: 300, // ms for input debouncing
  animationDuration: 200, // ms for UI animations
  toastDuration: 3000, // ms for toast notifications
  autoSaveDelay: 2000 // ms for auto-save functionality
} as const;

// Responsive breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
} as const;

// Color themes
export const THEMES = {
  light: {
    name: 'Light',
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0',
      accent: '#06b6d4'
    }
  },
  dark: {
    name: 'Dark',
    colors: {
      primary: '#60a5fa',
      secondary: '#94a3b8',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
      textSecondary: '#94a3b8',
      border: '#334155',
      accent: '#22d3ee'
    }
  }
} as const;

// Error messages
export const ERROR_MESSAGES = {
  textTooLong: `Text is too long. Maximum ${TEXT_LIMITS.maxLength.toLocaleString()} characters allowed.`,
  tooManyLines: `Too many lines. Maximum ${TEXT_LIMITS.maxLines.toLocaleString()} lines allowed.`,
  processingError: 'An error occurred while processing the text. Please try again.',
  copyError: 'Failed to copy text to clipboard. Please try again.',
  invalidInput: 'Invalid input provided.',
  networkError: 'Network error. Please check your connection and try again.'
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  textCopied: 'Text copied to clipboard!',
  textReset: 'Text has been reset.',
  operationComplete: 'Operation completed successfully.',
  settingsSaved: 'Settings saved successfully.'
} as const;

// Feature flags
export const FEATURE_FLAGS = {
  enableAdvancedFormatting: true,
  enableTextAnalytics: true,
  enableKeyboardShortcuts: true,
  enableThemeToggle: true,
  enableAutoSave: false, // Disabled by default
  enableExportFeatures: true
} as const;

// Regular expressions for text processing
export const REGEX_PATTERNS = {
  email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
  url: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
  phoneNumber: /(\+\d{1,3}[- ]?)?\d{10}/g,
  whitespace: /\s+/g,
  lineBreaks: /\r\n|\r|\n/g,
  multipleLineBreaks: /\n{3,}/g,
  specialCharacters: /[^\w\s.,!?;:'"()-]/g,
  numbers: /\d/g,
  punctuation: /[^\w\s]/g
} as const;

// Export grouped constants
export const CONSTANTS = {
  app: APP_CONFIG,
  keyboard: KEYBOARD_SHORTCUTS,
  settings: DEFAULT_SETTINGS,
  limits: TEXT_LIMITS,
  ui: UI_CONSTANTS,
  breakpoints: BREAKPOINTS,
  themes: THEMES,
  errors: ERROR_MESSAGES,
  success: SUCCESS_MESSAGES,
  features: FEATURE_FLAGS,
  regex: REGEX_PATTERNS
} as const;

export default CONSTANTS;
