/**
 * Text formatting and manipulation utilities for Text Formatter Pro
 * Provides functions for case conversion, text cleaning, and analysis
 */

// Text case conversion functions
export const textCaseUtils = {
  /**
   * Convert text to UPPERCASE
   */
  toUpperCase: (text: string): string => {
    return text.toUpperCase();
  },

  /**
   * Convert text to lowercase
   */
  toLowerCase: (text: string): string => {
    return text.toLowerCase();
  },

  /**
   * Convert text to Title Case (capitalize first letter of each word)
   */
  toTitleCase: (text: string): string => {
    return text.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  },

  /**
   * Convert text to Sentence case (capitalize first letter of each sentence)
   */
  toSentenceCase: (text: string): string => {
    return text.toLowerCase().replace(/(^\s*\w|[.!?]\s*\w)/g, (c) => 
      c.toUpperCase()
    );
  }
};

// Text cleaning and manipulation functions
export const textCleaningUtils = {
  /**
   * Remove extra whitespace (multiple spaces, tabs)
   */
  removeExtraWhitespace: (text: string): string => {
    return text.replace(/\s+/g, ' ');
  },

  /**
   * Remove extra line breaks and normalize line endings
   */
  normalizeLineBreaks: (text: string): string => {
    // First normalize all line endings to \n
    const normalized = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    // Then remove multiple consecutive line breaks
    return normalized.replace(/\n{3,}/g, '\n\n');
  },

  /**
   * Strip special characters with option to specify which characters to remove
   */
  stripSpecialCharacters: (text: string, charactersToRemove?: string): string => {
    if (charactersToRemove) {
      const escapedChars = charactersToRemove.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`[${escapedChars}]`, 'g');
      return text.replace(regex, '');
    }
    // Default: remove common special characters but keep basic punctuation
    return text.replace(/[^\w\s.,!?;:'"()-]/g, '');
  },

  /**
   * Trim leading and trailing whitespace
   */
  trimWhitespace: (text: string): string => {
    return text.trim();
  },

  /**
   * Remove all whitespace
   */
  removeAllWhitespace: (text: string): string => {
    return text.replace(/\s/g, '');
  },

  /**
   * Remove numbers from text
   */
  removeNumbers: (text: string): string => {
    return text.replace(/\d/g, '');
  },

  /**
   * Remove punctuation from text
   */
  removePunctuation: (text: string): string => {
    return text.replace(/[^\w\s]/g, '');
  }
};

// Text analysis functions
export const textAnalysisUtils = {
  /**
   * Count words in text
   */
  countWords: (text: string): number => {
    if (!text.trim()) return 0;
    return text.trim().split(/\s+/).length;
  },

  /**
   * Count characters with spaces
   */
  countCharactersWithSpaces: (text: string): number => {
    return text.length;
  },

  /**
   * Count characters without spaces
   */
  countCharactersWithoutSpaces: (text: string): number => {
    return text.replace(/\s/g, '').length;
  },

  /**
   * Count lines in text
   */
  countLines: (text: string): number => {
    if (!text) return 0;
    return text.split(/\r\n|\r|\n/).length;
  },

  /**
   * Count paragraphs in text
   */
  countParagraphs: (text: string): number => {
    if (!text.trim()) return 0;
    return text.trim().split(/\n\s*\n/).length;
  },

  /**
   * Count sentences in text
   */
  countSentences: (text: string): number => {
    if (!text.trim()) return 0;
    return text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).length;
  },

  /**
   * Estimate reading time in minutes (average 200 words per minute)
   */
  estimateReadingTime: (text: string): number => {
    const wordCount = textAnalysisUtils.countWords(text);
    const wordsPerMinute = 200;
    const readingTime = Math.ceil(wordCount / wordsPerMinute);
    return Math.max(1, readingTime); // Minimum 1 minute
  },

  /**
   * Get average word length
   */
  getAverageWordLength: (text: string): number => {
    if (!text.trim()) return 0;
    const words = text.trim().split(/\s+/);
    const totalLength = words.reduce((sum, word) => sum + word.replace(/[^\w]/g, '').length, 0);
    return Math.round((totalLength / words.length) * 10) / 10; // Round to 1 decimal place
  },

  /**
   * Get text statistics summary
   */
  getTextStatistics: (text: string) => {
    return {
      words: textAnalysisUtils.countWords(text),
      charactersWithSpaces: textAnalysisUtils.countCharactersWithSpaces(text),
      charactersWithoutSpaces: textAnalysisUtils.countCharactersWithoutSpaces(text),
      lines: textAnalysisUtils.countLines(text),
      paragraphs: textAnalysisUtils.countParagraphs(text),
      sentences: textAnalysisUtils.countSentences(text),
      readingTimeMinutes: textAnalysisUtils.estimateReadingTime(text),
      averageWordLength: textAnalysisUtils.getAverageWordLength(text)
    };
  }
};

// Combined utility functions
export const textFormatterUtils = {
  /**
   * Apply multiple formatting operations in sequence
   */
  formatText: (
    text: string,
    operations: {
      caseConversion?: 'upper' | 'lower' | 'title' | 'sentence';
      removeExtraWhitespace?: boolean;
      normalizeLineBreaks?: boolean;
      stripSpecialCharacters?: boolean | string;
      trimWhitespace?: boolean;
      removeNumbers?: boolean;
      removePunctuation?: boolean;
    }
  ): string => {
    let result = text;

    // Apply case conversion
    if (operations.caseConversion) {
      switch (operations.caseConversion) {
        case 'upper':
          result = textCaseUtils.toUpperCase(result);
          break;
        case 'lower':
          result = textCaseUtils.toLowerCase(result);
          break;
        case 'title':
          result = textCaseUtils.toTitleCase(result);
          break;
        case 'sentence':
          result = textCaseUtils.toSentenceCase(result);
          break;
      }
    }

    // Apply cleaning operations
    if (operations.removeExtraWhitespace) {
      result = textCleaningUtils.removeExtraWhitespace(result);
    }

    if (operations.normalizeLineBreaks) {
      result = textCleaningUtils.normalizeLineBreaks(result);
    }

    if (operations.stripSpecialCharacters) {
      if (typeof operations.stripSpecialCharacters === 'string') {
        result = textCleaningUtils.stripSpecialCharacters(result, operations.stripSpecialCharacters);
      } else {
        result = textCleaningUtils.stripSpecialCharacters(result);
      }
    }

    if (operations.removeNumbers) {
      result = textCleaningUtils.removeNumbers(result);
    }

    if (operations.removePunctuation) {
      result = textCleaningUtils.removePunctuation(result);
    }

    if (operations.trimWhitespace) {
      result = textCleaningUtils.trimWhitespace(result);
    }

    return result;
  }
};

// Export all utilities as a single object
export const textUtils = {
  case: textCaseUtils,
  cleaning: textCleaningUtils,
  analysis: textAnalysisUtils,
  formatter: textFormatterUtils
};

export default textUtils;
