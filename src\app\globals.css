@import "tailwindcss";

:root {
    --background: #ffffff;
    --foreground: #1e293b;
    --primary: #3b82f6;
    --secondary: #64748b;
    --accent: #06b6d4;
    --border: #e2e8f0;
    --surface: #f8fafc;
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-primary: var(--primary);
    --color-secondary: var(--secondary);
    --color-accent: var(--accent);
    --color-border: var(--border);
    --color-surface: var(--surface);
    --font-sans: var(--font-inter);
    --font-mono: var(--font-jetbrains-mono);
}

@media (prefers-color-scheme: dark) {
    :root {
        --background: #0f172a;
        --foreground: #f1f5f9;
        --primary: #60a5fa;
        --secondary: #94a3b8;
        --accent: #22d3ee;
        --border: #334155;
        --surface: #1e293b;
    }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
}

/* Focus styles for accessibility */
*:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Custom button animations */
.btn-animate {
    transition: all 0.2s ease-in-out;
}

.btn-animate:hover {
    transform: translateY(-1px);
}

.btn-animate:active {
    transform: translateY(0);
}

/* Text area resize handle styling */
textarea {
    resize: vertical;
}

/* Selection styling */
::selection {
    background-color: var(--primary);
    color: white;
}

/* Loading animation */
@keyframes pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}
