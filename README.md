# Text Formatter Pro

A powerful, web-based text formatting and manipulation tool designed for writers, developers, content creators, and marketers. Transform, clean, and analyze your text with an intuitive interface and comprehensive feature set.

![Text Formatter Pro](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-4.0-38B2AC.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

## 🚀 Features

### Text Case Conversion

-   **UPPERCASE** - Convert all text to uppercase letters
-   **lowercase** - Convert all text to lowercase letters
-   **Title Case** - Capitalize the first letter of each word
-   **Sentence case** - Capitalize the first letter of each sentence

### Text Cleaning & Manipulation

-   **Remove Extra Whitespace** - Clean up multiple spaces and tabs
-   **Normalize Line Breaks** - Standardize line endings and remove excessive breaks
-   **Strip Special Characters** - Remove unwanted special characters (customizable)
-   **Trim Whitespace** - Remove leading and trailing spaces
-   **Remove Numbers** - Strip all numeric characters
-   **Remove Punctuation** - Clean text of punctuation marks

### Real-time Text Analysis

-   **Word Counter** - Live count of words in your text
-   **Character Counter** - Count with and without spaces
-   **Line Counter** - Track number of lines
-   **Paragraph Counter** - Count text paragraphs
-   **Sentence Counter** - Analyze sentence structure
-   **Reading Time Estimation** - Calculate estimated reading time
-   **Average Word Length** - Statistical analysis of word complexity

### User Experience

-   **Split-pane Layout** - Intuitive input/output interface
-   **Real-time Preview** - See changes as you type
-   **Keyboard Shortcuts** - Quick access to all functions
-   **Copy to Clipboard** - One-click text copying
-   **Responsive Design** - Works on all devices
-   **Dark/Light Mode** - Automatic theme detection
-   **Accessibility Compliant** - WCAG 2.1 AA standards

## 🛠️ Tech Stack

-   **Framework**: Next.js 15.3.5 with App Router
-   **Language**: TypeScript 5.0
-   **Styling**: Tailwind CSS 4.0
-   **Fonts**: Inter & JetBrains Mono
-   **Icons**: Heroicons (SVG)
-   **Deployment**: Vercel-ready

## 📋 Prerequisites

Before running this project, ensure you have:

-   **Node.js** 18.0 or higher
-   **npm** 9.0 or higher (or yarn/pnpm equivalent)
-   Modern web browser with JavaScript enabled

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/chirag127/Text-Formatter-Pro.git
cd Text-Formatter-Pro
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Run Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

### 4. Open in Browser

Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
Text-Formatter-Pro/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Main page
│   ├── components/            # React components
│   │   ├── ui/               # Reusable UI components
│   │   │   ├── Button.tsx
│   │   │   ├── TextArea.tsx
│   │   │   └── StatisticsCard.tsx
│   │   ├── ControlPanel.tsx   # Formatting controls
│   │   ├── StatisticsPanel.tsx # Text analysis display
│   │   ├── ErrorBoundary.tsx  # Error handling
│   │   └── KeyboardShortcutsHelp.tsx
│   ├── hooks/                 # Custom React hooks
│   │   └── useKeyboardShortcuts.ts
│   ├── lib/                   # Utility libraries
│   │   ├── textUtils.ts       # Text processing functions
│   │   ├── constants.ts       # App constants
│   │   ├── validation.ts      # Input validation
│   │   └── accessibility.ts   # A11y utilities
│   └── types/                 # TypeScript definitions
│       └── index.ts
├── public/                    # Static assets
├── package.json              # Dependencies and scripts
├── tsconfig.json            # TypeScript configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── next.config.ts           # Next.js configuration
└── README.md               # This file
```

## ⌨️ Keyboard Shortcuts

| Shortcut           | Action                   |
| ------------------ | ------------------------ |
| `Ctrl + U`         | Convert to UPPERCASE     |
| `Ctrl + L`         | Convert to lowercase     |
| `Ctrl + T`         | Convert to Title Case    |
| `Ctrl + S`         | Convert to Sentence case |
| `Ctrl + W`         | Remove extra whitespace  |
| `Ctrl + N`         | Normalize line breaks    |
| `Ctrl + R`         | Reset/Clear text         |
| `Ctrl + Shift + C` | Copy formatted text      |
| `Escape`           | Clear selection          |

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the root directory for any environment-specific configurations:

```env
# Example environment variables (none required for basic functionality)
NEXT_PUBLIC_APP_NAME="Text Formatter Pro"
NEXT_PUBLIC_APP_VERSION="1.0.0"
```

### Customization

The application can be customized by modifying:

-   **Colors & Themes**: Edit `src/app/globals.css` and `src/lib/constants.ts`
-   **Text Processing**: Extend functions in `src/lib/textUtils.ts`
-   **Keyboard Shortcuts**: Modify `src/lib/constants.ts` and `src/hooks/useKeyboardShortcuts.ts`
-   **UI Components**: Customize components in `src/components/`

## 🧪 Testing

Run the type checker:

```bash
npm run type-check
```

Run the linter:

```bash
npm run lint
```

## 🚀 Deployment

### Deploy to Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy with one click

### Manual Deployment

1. Build the application:

    ```bash
    npm run build
    ```

2. Start the production server:
    ```bash
    npm start
    ```

### Static Export

For static hosting:

1. Add to `next.config.ts`:

    ```typescript
    /** @type {import('next').NextConfig} */
    const nextConfig = {
        output: "export",
        trailingSlash: true,
        images: {
            unoptimized: true,
        },
    };
    ```

2. Build and export:
    ```bash
    npm run build
    ```

## 🎯 Key Features Explained

### Real-time Processing

All text transformations happen instantly as you type or click buttons, providing immediate feedback.

### Accessibility First

-   Full keyboard navigation support
-   Screen reader compatibility
-   High contrast color schemes
-   ARIA labels and descriptions
-   Focus management

### Performance Optimized

-   Efficient text processing algorithms
-   Debounced input handling
-   Minimal re-renders
-   Optimized bundle size

### Error Handling

-   Comprehensive input validation
-   Graceful error recovery
-   User-friendly error messages
-   Development error details

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

### Development Guidelines

1. Follow TypeScript best practices
2. Maintain accessibility standards
3. Write descriptive commit messages
4. Test your changes thoroughly
5. Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Chirag Singhal** ([@chirag127](https://github.com/chirag127))

## 🙏 Acknowledgments

-   [Next.js](https://nextjs.org/) for the amazing React framework
-   [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
-   [Heroicons](https://heroicons.com/) for the beautiful SVG icons
-   [Vercel](https://vercel.com/) for seamless deployment

## 📊 Project Stats

-   **Lines of Code**: ~2,000+
-   **Components**: 10+
-   **Utility Functions**: 25+
-   **Keyboard Shortcuts**: 9
-   **Text Operations**: 15+

---

**Last Updated**: 2025-07-11T17:00:13.112Z

Made with ❤️ by [Chirag Singhal](https://github.com/chirag127)
