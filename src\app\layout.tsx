import type { Metadata } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import ErrorBoundary from "@/components/ErrorBoundary";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Text Formatter Pro",
  description: "A web-based text formatting and manipulation tool for writers, developers, content creators, and marketers",
  keywords: ["text formatter", "text editor", "case converter", "text cleaner", "word counter", "text analysis"],
  authors: [{ name: "<PERSON><PERSON>", url: "https://github.com/chirag127" }],
  creator: "<PERSON><PERSON> Singhal",
  publisher: "<PERSON><PERSON>al",
  robots: "index, follow",
  viewport: "width=device-width, initial-scale=1",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" }
  ],
  openGraph: {
    type: "website",
    title: "Text Formatter Pro",
    description: "A web-based text formatting and manipulation tool for writers, developers, content creators, and marketers",
    siteName: "Text Formatter Pro",
  },
  twitter: {
    card: "summary_large_image",
    title: "Text Formatter Pro",
    description: "A web-based text formatting and manipulation tool for writers, developers, content creators, and marketers",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100`}
      >
        <ErrorBoundary>
          <div className="min-h-full">
            {children}
          </div>
        </ErrorBoundary>
      </body>
    </html>
  );
}
